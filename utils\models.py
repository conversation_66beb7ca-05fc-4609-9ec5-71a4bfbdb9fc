"""
Model utilities for loading and managing YOLO and ActionTransformer models
"""

import torch
import numpy as np
from ultralytics import YOLO
from typing import Optional, <PERSON><PERSON>


def load_yolo_model(model_path: str, device, use_half_precision: bool = False) -> Optional[YOLO]:
    """
    Load and initialize YOLO model

    Args:
        model_path: Path to YOLO model file
        device: PyTorch device or device index
        use_half_precision: Whether to use half precision (FP16)

    Returns:
        Initialized YOLO model or None if failed
    """
    try:
        yolo_model = YOLO(model_path)

        # Convert device to proper format for YOLO
        if isinstance(device, torch.device):
            device_str = str(device)
        else:
            device_str = device

        # Check if this is a TensorRT model
        is_tensorrt = model_path.endswith('.engine')

        if is_tensorrt:
            # TensorRT models don't support .to() or .half() methods
            # They are already optimized and device-specific
            print(f"✅ TensorRT YOLO model loaded from {model_path}")
            print("ℹ️  TensorRT models are pre-optimized and device-specific")
        else:
            # Regular PyTorch model - can apply device and precision settings
            yolo_model.to(device_str)

            # Apply half precision if requested and on CUDA
            if use_half_precision and ('cuda' in str(device_str) or device_str == 0):
                try:
                    yolo_model.half()
                    print(f"✅ YOLO model loaded with half precision from {model_path}")
                except Exception as e:
                    print(f"⚠️ Could not apply half precision to YOLO: {e}")
                    print(f"✅ YOLO model loaded (full precision) from {model_path}")
            else:
                print(f"✅ YOLO model loaded from {model_path}")

        return yolo_model

    except Exception as e:
        print(f"❌ Error loading YOLO model: {e}")
        return None


def load_classifier(model_path: str, device) -> Optional[object]:
    """
    Load and initialize Classifier model (3D CNN)

    Args:
        model_path: Path to Classifier checkpoint
        device: PyTorch device

    Returns:
        Initialized Classifier model or None if failed
    """
    try:
        # Import the Classifier from cnn.py
        import sys
        import os
        # Add the parent directory to sys.path to import cnn.py
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
            sys.path.append(parent_dir)

        from cnn import Classifier

        model = Classifier()
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        model.to(device).eval()
        model.backbone.eval()

        print(f"✅ Classifier model loaded from {model_path}")
        return model

    except Exception as e:
        print(f"❌ Error loading Classifier model: {e}")
        return None


def load_action_transformer(model_path: str, device) -> Optional[object]:
    """
    Load and initialize Classifier model (3D CNN)

    Note: This function is kept for backward compatibility.
    The actual model loaded is the Classifier from cnn.py, not ActionTransformer.

    Args:
        model_path: Path to Classifier checkpoint
        device: PyTorch device

    Returns:
        Initialized Classifier model or None if failed
    """
    return load_classifier(model_path, device)


def initialize_models(classifier_path: str, yolo_path: str,
                     device) -> Tuple[Optional[object], Optional[YOLO]]:
    """
    Initialize both Classifier and YOLO models

    Args:
        classifier_path: Path to Classifier checkpoint
        yolo_path: Path to YOLO model
        device: PyTorch device

    Returns:
        Tuple of (Classifier model, YOLO model)
    """
    print("Loading models...")

    # Load Classifier
    action_model = load_classifier(classifier_path, device)

    # Load YOLO
    yolo_model = load_yolo_model(yolo_path, device, use_half_precision=False)

    if action_model is not None and yolo_model is not None:
        print("✅ All models loaded successfully!")
    else:
        print("❌ Some models failed to load!")

    return action_model, yolo_model


def get_device(prefer_gpu: bool = True) -> torch.device:
    """
    Get the best available device
    
    Args:
        prefer_gpu: Whether to prefer GPU if available
    
    Returns:
        PyTorch device
    """
    if prefer_gpu and torch.cuda.is_available():
        device = torch.device('cuda:0')
        print(f"Using GPU: {torch.cuda.get_device_name(0)}")
    else:
        device = torch.device('cpu')
        print("Using CPU")
    
    return device


def warm_up_models(action_model, yolo_model, device, frame_shape: Tuple[int, int, int] = (480, 640, 3)):
    """
    Warm up models with dummy data for consistent performance

    Args:
        action_model: Classifier model
        yolo_model: YOLO model
        device: PyTorch device
        frame_shape: Shape for dummy frame (height, width, channels)
    """
    print("Warming up models...")

    try:
        # Warm up YOLO
        if yolo_model is not None:
            dummy_frame = np.zeros(frame_shape, dtype=np.uint8)
            _ = yolo_model.track(dummy_frame, persist=True, classes=[0], verbose=False)
            print("✅ YOLO model warmed up")

        # Warm up Classifier
        if action_model is not None:
            dummy_tensor = torch.randn(1, 3, 16, 112, 112).to(device)
            with torch.no_grad():
                _ = action_model(dummy_tensor)
            print("✅ Classifier model warmed up")

    except Exception as e:
        print(f"Warning: Model warmup failed: {e}")


def validate_models(action_model, yolo_model) -> bool:
    """
    Validate that models are properly loaded and functional

    Args:
        action_model: Classifier model
        yolo_model: YOLO model

    Returns:
        True if both models are valid, False otherwise
    """
    if action_model is None:
        print("❌ Classifier model is not loaded")
        return False

    if yolo_model is None:
        print("❌ YOLO model is not loaded")
        return False

    try:
        # Test Classifier
        dummy_tensor = torch.randn(1, 3, 16, 112, 112)
        if hasattr(action_model, 'device'):
            dummy_tensor = dummy_tensor.to(action_model.device)

        with torch.no_grad():
            output = action_model(dummy_tensor)
            if output is None or output.shape[0] != 1:
                print("❌ Classifier model output is invalid")
                return False

        # Test YOLO
        dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        results = yolo_model(dummy_frame, verbose=False)
        if results is None:
            print("❌ YOLO model output is invalid")
            return False

        print("✅ Both models validated successfully")
        return True

    except Exception as e:
        print(f"❌ Model validation failed: {e}")
        return False


def get_model_info(action_model, yolo_model) -> dict:
    """
    Get information about loaded models

    Args:
        action_model: Classifier model
        yolo_model: YOLO model

    Returns:
        Dictionary with model information
    """
    info = {
        'classifier': {
            'loaded': action_model is not None,
            'device': str(action_model.device) if action_model and hasattr(action_model, 'device') else 'unknown',
            'parameters': sum(p.numel() for p in action_model.parameters()) if action_model else 0
        },
        'yolo': {
            'loaded': yolo_model is not None,
            'device': str(yolo_model.device) if yolo_model and hasattr(yolo_model, 'device') else 'unknown',
            'model_name': yolo_model.model_name if yolo_model and hasattr(yolo_model, 'model_name') else 'unknown'
        }
    }

    return info

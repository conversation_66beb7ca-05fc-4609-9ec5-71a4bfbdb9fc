# Multi-threaded Video Processing System

A high-performance, multi-threaded video processing system for real-time person detection, tracking, and action classification across multiple video sources.

## Architecture

The system uses a 4-thread architecture for maximum GPU utilization:

1. **VideoSource Threads**: Each video source runs in its own thread, capturing frames and storing them in individual queues
2. **Detection Thread**: Reads frames from all video source queues, detects persons using YOLO with built-in tracking, crops detected persons, and stores crops in person-specific queues
3. **Prediction Thread**: Reads 16-frame clips from person queues, performs batched video classification using a 3D CNN model, and stores predictions in label queues
4. **Display Thread**: Annotates original video frames with bounding boxes and predicted labels, displays all video sources in a single window with grid layout

## Features

- **Multi-source Processing**: Process multiple video files simultaneously
- **Grid Layout Display**: All video sources displayed in a single window with automatic grid arrangement
- **Real-time Person Tracking**: Uses YOLO's built-in ByteTrack for efficient person tracking
- **Batched Prediction**: Dynamic batching based on number of active persons for optimal GPU utilization
- **GPU Optimization**: All models, tensors, and frames are processed directly on GPU to minimize CPU-GPU memory transfers
- **TensorRT Support**: Optional TensorRT acceleration for YOLO model
- **Half Precision**: Optional FP16 mode for increased performance
- **Configurable Settings**: Easy-to-modify configuration at the top of the script
- **Toggle Person IDs**: Runtime toggle for showing/hiding person ID annotations

## Configuration

Edit the configuration section at the top of `multi_threaded_video_processor.py`:

```python
# Video sources - modify this list to add your video sources
VIDEO_SOURCES = [
    "data/feed/video1.mp4",
    "data/feed/video2.mp4",
    # Add more video sources as needed
]

# Model paths
YOLO_PATH = 'data/models/yolo11s.pt'
YOLO_TRT_PATH = 'data/models/yolo11s.engine'
VIDEO_CLASSIFIER_PATH = 'data/models/trained_model_cnn.pt'

# Display settings
DISPLAY_WIDTH = 640
DISPLAY_HEIGHT = 480

# Processing settings
QUEUE_SIZE = 100
CLIP_FRAMES = 16
CONFIDENCE_THRESHOLD = 0.3
CROP_SIZE = (224, 224)

# Toggle options
USE_TENSORRT = False  # Set to True to use TensorRT YOLO model
USE_HALF_PRECISION = True  # Set to True to use FP16 precision
VERBOSE = False  # Set to True for detailed logging
SHOW_PERSON_IDS = True  # Set to True to show person IDs in annotations
```

## Usage

### Basic Usage

```bash
python multi_threaded_video_processor.py
```

### Test the System

```bash
python test_multi_threaded_processor.py
```

### Controls

- **'q'**: Quit the application
- **'p'**: Pause/Resume processing
- **'i'**: Toggle person ID display on/off

## Requirements

- Python 3.8+
- PyTorch with CUDA support
- OpenCV
- Ultralytics YOLO
- NumPy

## Model Setup

1. **YOLO Model**: Place your YOLO model at `data/models/yolo11s.pt`
2. **TensorRT Model** (optional): Place TensorRT-compiled YOLO model at `data/models/yolo11s.engine`
3. **Video Classifier**: Place your trained 3D CNN model at `data/models/trained_model_cnn.pt`

### Creating TensorRT Model

To create a TensorRT-optimized YOLO model:

```python
from ultralytics import YOLO

# Load the YOLO model
model = YOLO("data/models/yolo11s.pt")

# Export to TensorRT format
model.export(format="engine", device=0, nms=True)
```

## Performance Optimization

### GPU Memory Optimization
- All models are loaded directly to GPU
- Frame crops are converted to GPU tensors immediately
- Batched processing minimizes GPU kernel launches
- Half precision mode reduces memory usage

### Threading Optimization
- Each video source has its own capture thread
- Person-specific queues enable parallel processing
- Dynamic batching adapts to the number of active persons
- Lock-free queue operations where possible

### Configuration Tips
- Increase `QUEUE_SIZE` for better buffering (uses more memory)
- Decrease `CONFIDENCE_THRESHOLD` for more detections
- Enable `USE_HALF_PRECISION` for faster processing
- Enable `USE_TENSORRT` for maximum YOLO performance

## Output

The system provides:
- Real-time video display in grid layout with bounding boxes and labels
- Individual source identification and performance metrics per video
- Performance metrics printed to terminal at the end
- Suspicious behavior alerts (when `VERBOSE=True`)
- Runtime toggle for person ID annotations

### Performance Metrics
```
📊 PERFORMANCE METRICS
==================================================
⏱️  Total processing time: 120.45 seconds
🎬 Total frames processed: 3612
📈 Average FPS: 29.98
👁️  Total detections made: 1205
🧠 Total predictions made: 89
👤 Active person trackers: 3
==================================================
```

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**: Reduce `QUEUE_SIZE` or disable `USE_HALF_PRECISION`
2. **Low FPS**: Enable `USE_TENSORRT` and `USE_HALF_PRECISION`
3. **No Detections**: Lower `CONFIDENCE_THRESHOLD`
4. **Video Not Found**: Check video paths in `VIDEO_SOURCES`

### Debug Mode

Enable verbose logging by setting `VERBOSE = True` for detailed information about:
- Thread startup/shutdown
- New person detections
- Suspicious behavior alerts
- Queue status information

## Architecture Details

### Thread Communication
- **Frame Queues**: Each video source has a frame queue
- **Person Queues**: Each detected person has a crop queue and label queue
- **Display Data**: Shared dictionary with thread-safe access using locks

### Memory Management
- Automatic cleanup of inactive person trackers
- Queue size limits prevent memory overflow
- GPU memory is managed by PyTorch's memory allocator

### Error Handling
- Graceful handling of YOLO NMS errors
- Automatic recovery from temporary processing failures
- Clean shutdown on user interrupt or errors

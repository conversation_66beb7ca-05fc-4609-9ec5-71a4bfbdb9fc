#!/usr/bin/env python3
"""
Test script for the multi-threaded video processor
"""

import os
import sys
from multi_threaded_video_processor import MultiThreadedVideoProcessor, VIDEO_SOURCES

def test_processor():
    """Test the multi-threaded video processor"""
    
    # Check if video sources exist
    valid_sources = []
    for source in VIDEO_SOURCES:
        if os.path.exists(source):
            valid_sources.append(source)
            print(f"✅ Found video source: {source}")
        else:
            print(f"⚠️ Video source not found: {source}")
    
    if not valid_sources:
        print("❌ No valid video sources found. Please update VIDEO_SOURCES in multi_threaded_video_processor.py")
        print("   Example sources you can add:")
        print("   - 'data/feed/video1.mp4'")
        print("   - 'data/feed/video2.mp4'")
        print("   - Or any other video file paths")
        return False
    
    try:
        # Create processor instance
        processor = MultiThreadedVideoProcessor()
        
        # Initialize models
        print("🔄 Initializing models...")
        processor.initialize_models()
        
        # Setup video sources
        print("📹 Setting up video sources...")
        processor.setup_video_sources(valid_sources)
        
        print(f"\n✅ Successfully initialized processor with {len(processor.video_sources)} video sources")
        print("🎮 Ready to start processing!")
        print("   Controls: 'q' to quit, 'p' to pause/resume")
        
        # Start processing
        processor.start_processing()
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Multi-threaded Video Processor")
    print("="*50)
    
    success = test_processor()
    
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
        sys.exit(1)

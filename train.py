import utils.videodataset as vd
from torch.utils.data import Data<PERSON>oader
import torch
from torch import nn,optim
from sklearn.metrics import accuracy_score, confusion_matrix, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
import os
import cnn

train_path = 'data/MNNIT/train_annot.csv'
valid_path = 'data/MNNIT/val_annot.csv'

model_path = 'data/models/trained_model_cnn.pt'
device = torch.device('cuda:0')

#Training parameters
batch_size = 8
num_epochs = 10
num_frames = 16
lr = 1e-5
weight_decay = 1e-4


#Adding guard against new subprocess start error
if __name__=='__main__':
    #Load video annotation files
    train_data = vd.VideoDataset(train_path, num_frames=num_frames)

    test_data = vd.ValidDataset(valid_path, num_frames=num_frames)

    #Create dataloaders for training and testing
    train_loader = DataLoader(train_data,
                                batch_size=batch_size,
                                num_workers=4,
                                pin_memory=True,
                                pin_memory_device='cuda:0',
                                shuffle=True,
                                drop_last=False)

    val_loader = DataLoader(test_data,
                                batch_size=batch_size,
                                num_workers=4,
                                pin_memory=True,
                                pin_memory_device='cuda:0',
                                shuffle=False)

    #Creating action transformer model
    
    # Initialize model
    model = cnn.Classifier().to(device)
    criterion = nn.BCEWithLogitsLoss().to(device)
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=weight_decay)
    
    # Resume from checkpoint if it exists
    if os.path.exists(model_path):
        print("Loading checkpoint...")
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        best_val_loss = checkpoint.get('sval_loss', float('inf'))
        print(f"Resumed from epoch {start_epoch}, best val loss: {best_val_loss:.4f}")
    else:
        print("No checkpoint found. Starting from scratch.")
        start_epoch = 0
        best_val_loss = float('inf')

    for epoch in range(start_epoch, num_epochs):
        # Training
        print(f"Epoch {epoch+1}/{num_epochs} - Starting training...")
        model.float()
        model.train()
        model.backbone.train()
        running_loss = 0.0
        train_preds, train_labels = [], []

        for inputs, labels in train_loader:
            inputs = inputs.to(device)
            labels = labels.float().to(device).unsqueeze(1)
            optimizer.zero_grad(set_to_none=True)
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()

            probs = torch.sigmoid(outputs)
            preds = (probs > 0.5).long()

            train_preds.extend(preds.cpu().numpy().flatten())
            train_labels.extend(labels.cpu().numpy().flatten())

        avg_loss = running_loss / len(train_loader)
        train_accuracy = accuracy_score(train_labels, train_preds)

        # Validation
        model.eval()
        model.backbone.eval()
        val_loss = 0.0
        val_preds, val_labels = [], []

        with torch.no_grad():
          for inputs, labels in val_loader:
                inputs = inputs.to(device)
                labels = labels.float().to(device).unsqueeze(1)

                outputs = model(inputs)
                loss = criterion(outputs, labels)
                val_loss += loss.item()

                probs = torch.sigmoid(outputs)
                preds = (probs > 0.5).long()

                val_preds.extend(preds.cpu().numpy().flatten())
                val_labels.extend(labels.cpu().numpy().flatten())

        val_accuracy = accuracy_score(val_labels, val_preds)
        print(classification_report(val_labels, val_preds, digits=4))
        avg_val_loss = val_loss / len(val_loader)

        print(f"Epoch [{epoch+1}/{num_epochs}] | "
              f"Train Loss: {avg_loss:.4f} | "
              f"Val Loss: {avg_val_loss:.4f} | "
              f"Train Acc: {100 * train_accuracy:.2f}% | "
              f"Val Acc: {100 * val_accuracy:.2f}%")

        # Save the model if it improves validation loss
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            print("Validation loss improved. Saving model...")
            model.half()  # Optional: cast model to half precision for saving
            torch.save({
                'epoch': epoch,
                'sval_loss': round(best_val_loss,4),
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': loss,
                'train_accuracy': train_accuracy,
                'val_accuracy': val_accuracy
            }, model_path)
            print("Model saved!")
        else:
            print("Validation loss did not improve.")
        # Confusion matrix plot
        cm = confusion_matrix(val_labels, val_preds)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=['Normal', 'Shoplifting'],
                    yticklabels=['Normal', 'Shoplifting'],
                    cbar=False, square=True
                    )
        plt.xlabel('Predicted')
        plt.ylabel('True')
        plt.title(f'Epoch {epoch+1} Confusion Matrix')
        impath = f'data/results/vanilla/Epoch_{epoch+1}_CM.png'
        plt.savefig(impath)
        plt.close('all')
#!/usr/bin/env python3
"""
Multi-Source Video Processor with Person Tracking and Action Classification
"""

import cv2
import os
import time
import threading
import queue
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import torch
import argparse

# Import required modules
from utils.detection import detect_persons
from utils.cropping import crop_person
from utils.models import load_action_transformer, load_yolo_model
from utils.prediction import predict_with_timing

# ============================================================================
# 📹 CONFIGURATION - MODIFY THESE SETTINGS:
# ============================================================================

# Video sources - add your video files/streams here
VIDEO_SOURCES = [
    "data/feed/video1.mp4",
    "data/feed/video2.mp4",
    # Add more sources as needed
]

# Model paths
DEFAULT_YOLO_PATH = 'data/models/yolo11s.pt'
DEFAULT_YOLO_TRT_PATH = 'data/models/yolo11s.engine'
DEFAULT_MODEL_PATH = 'data/models/trained_model_cnn.pt'

# Display settings
DEFAULT_DISPLAY_WIDTH = 1280
DEFAULT_DISPLAY_HEIGHT = 720

# Processing settings
DEFAULT_QUEUE_SIZE = 50
DEFAULT_CLIP_FRAMES = 16
DEFAULT_CONFIDENCE_THRESHOLD = 0.3
DEFAULT_CROP_SIZE = (224, 224)

# Performance settings
USE_TENSORRT = False  # Set to True to use TensorRT YOLO model
USE_HALF_PRECISION = False  # Set to True to use FP16 precision
VERBOSE = False  # Set to True for detailed logging

# ============================================================================

class VideoSource:
    """Captures frames from a video source and puts them in a queue"""
    
    def __init__(self, source_id: int, source_path: str, queue_size: int = DEFAULT_QUEUE_SIZE):
        self.source_id = source_id
        self.source_path = source_path
        self.frame_queue = queue.Queue(maxsize=queue_size)
        self.stop_event = threading.Event()
        self.thread = None
        self.source_name = os.path.basename(source_path)
        
    def start(self):
        """Start the video capture thread"""
        self.thread = threading.Thread(target=self._capture_frames, name=f"Source-{self.source_id}")
        self.thread.daemon = True
        self.thread.start()
        return self
        
    def _capture_frames(self):
        """Thread function to capture frames and add to queue"""
        cap = cv2.VideoCapture(self.source_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {self.source_path}")
            return
            
        frame_count = 0
        
        while not self.stop_event.is_set():
            ret, frame = cap.read()
            if not ret:
                print(f"📹 Video ended: {self.source_name}")
                break
                
            # Add timestamp to frame metadata
            timestamp = time.time()
            frame_data = {
                'frame': frame,
                'timestamp': timestamp,
                'frame_count': frame_count,
                'source_id': self.source_id,
                'source_name': self.source_name
            }
            
            # Try to add to queue, drop frames if queue is full
            try:
                self.frame_queue.put(frame_data, block=False)
            except queue.Full:
                pass  # Drop frame if queue is full
                
            frame_count += 1
            
        cap.release()
        
    def stop(self):
        """Stop the video capture thread"""
        self.stop_event.set()
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=1.0)

class PersonTracker:
    """Tracks persons across frames and manages person-specific queues"""
    
    def __init__(self, person_id: int, queue_size: int = DEFAULT_QUEUE_SIZE):
        self.person_id = person_id
        self.frame_queue = queue.Queue(maxsize=queue_size)
        self.label_queue = queue.Queue(maxsize=queue_size)
        self.last_seen = time.time()
        self.frames_processed = 0
        
    def add_frame(self, frame):
        """Add a cropped frame to this person's queue"""
        try:
            self.frame_queue.put(frame, block=False)
            self.last_seen = time.time()
            self.frames_processed += 1
            return True
        except queue.Full:
            return False
            
    def add_label(self, label, confidence):
        """Add a prediction label to this person's queue"""
        try:
            self.label_queue.put((label, confidence), block=False)
            return True
        except queue.Full:
            return False
            
    def get_latest_label(self):
        """Get the most recent label without removing it from queue"""
        if self.label_queue.empty():
            return None, 0.0
        
        # Get all labels from queue
        labels = []
        while not self.label_queue.empty():
            try:
                labels.append(self.label_queue.get(block=False))
                self.label_queue.task_done()
            except queue.Empty:
                break
                
        # Put the last label back in the queue
        if labels:
            last_label = labels[-1]
            try:
                self.label_queue.put(last_label, block=False)
            except queue.Full:
                pass
            return last_label
        
        return None, 0.0

class VideoProcessor:
    """Main processor that coordinates all threads and processing"""
    
    def __init__(self, 
                 video_sources: List[str],
                 model_path: str = DEFAULT_MODEL_PATH,
                 yolo_path: str = DEFAULT_YOLO_PATH,
                 yolo_trt_path: str = DEFAULT_YOLO_TRT_PATH,
                 use_tensorrt: bool = False,
                 display_width: int = DEFAULT_DISPLAY_WIDTH,
                 display_height: int = DEFAULT_DISPLAY_HEIGHT,
                 use_half_precision: bool = False,
                 verbose: bool = False):
        
        self.video_sources = []
        self.person_trackers = {}
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        
        # Configuration
        self.model_path = model_path
        self.yolo_path = yolo_trt_path if use_tensorrt else yolo_path
        self.use_tensorrt = use_tensorrt
        self.display_width = display_width
        self.display_height = display_height
        self.use_half_precision = use_half_precision
        self.verbose = verbose
        
        # Initialize video sources
        for i, source_path in enumerate(video_sources):
            self.video_sources.append(VideoSource(i, source_path))
            
        # Initialize models and device
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.action_model = None
        self.yolo_model = None
        
        # Performance tracking
        self.detection_times = []
        self.prediction_times = []
        self.display_times = []
        
        # Thread management
        self.threads = []
        
    def initialize_models(self):
        """Initialize YOLO and action classification models"""
        print(f"🔄 Initializing models on {self.device}...")
        
        # Load YOLO model
        self.yolo_model = load_yolo_model(
            self.yolo_path, 
            device=0 if self.device.type == 'cuda' else 'cpu',
            use_tensorrt=self.use_tensorrt
        )
        
        # Load action classification model
        self.action_model = load_action_transformer(self.model_path, self.device)
        
        # Apply half precision if requested and on CUDA
        if self.use_half_precision and self.device.type == 'cuda':
            print("🔄 Using half precision (FP16)...")
            self.action_model = self.action_model.half()
            
        print("✅ Models initialized successfully")
        
    def detection_thread(self):
        """Thread for person detection and tracking"""
        print("🔍 Starting detection thread...")
        
        while not self.stop_event.is_set():
            if self.pause_event.is_set():
                time.sleep(0.1)
                continue
                
            # Process frames from all video sources
            for source in self.video_sources:
                try:
                    frame_data = source.frame_queue.get(block=False)
                except queue.Empty:
                    continue
                    
                frame = frame_data['frame']
                source_id = frame_data['source_id']
                
                # Detect persons
                start_time = time.time()
                boxes, confidences, ids = detect_persons(
                    self.yolo_model, 
                    frame, 
                    confidence_threshold=DEFAULT_CONFIDENCE_THRESHOLD,
                    device_index=0 if self.device.type == 'cuda' else None
                )
                detection_time = time.time() - start_time
                self.detection_times.append(detection_time)
                
                # Process each detected person
                for box, conf, person_id in zip(boxes, confidences, ids):
                    # Create a unique ID combining source and person ID
                    unique_id = f"{source_id}_{person_id}"
                    
                    # Create tracker if this is a new person
                    if unique_id not in self.person_trackers:
                        self.person_trackers[unique_id] = PersonTracker(unique_id)
                        
                    # Crop person and add to their queue
                    crop = crop_person(frame, box, DEFAULT_CROP_SIZE)
                    if crop is not None:
                        self.person_trackers[unique_id].add_frame({
                            'crop': crop,
                            'box': box,
                            'confidence': conf,
                            'timestamp': frame_data['timestamp'],
                            'source_id': source_id,
                            'frame': frame  # Store reference to original frame
                        })
                
                # Add processed frame to display queue with detection info
                frame_data['detections'] = {
                    'boxes': boxes,
                    'confidences': confidences,
                    'ids': ids
                }
                
                # Mark task as done
                source.frame_queue.task_done()
                
            # Small sleep to prevent CPU hogging
            time.sleep(0.001)
            
    def prediction_thread(self):
        """Thread for action classification from person clips"""
        print("🧠 Starting prediction thread...")
        
        while not self.stop_event.is_set():
            if self.pause_event.is_set():
                time.sleep(0.1)
                continue
                
            # Get all active person trackers
            active_trackers = list(self.person_trackers.values())
            if not active_trackers:
                time.sleep(0.1)
                continue
                
            # Process each person tracker that has enough frames
            for tracker in active_trackers:
                # Check if we have enough frames for a clip
                if tracker.frame_queue.qsize() >= DEFAULT_CLIP_FRAMES:
                    # Extract frames for the clip
                    frames = []
                    frame_data = None
                    for _ in range(DEFAULT_CLIP_FRAMES):
                        try:
                            frame_data = tracker.frame_queue.get(block=False)
                            frames.append(frame_data['crop'])
                            tracker.frame_queue.task_done()
                        except queue.Empty:
                            break
                    
                    if len(frames) < DEFAULT_CLIP_FRAMES or frame_data is None:
                        continue
                        
                    # Convert frames to tensor and move to GPU
                    try:
                        # Stack frames into a clip tensor [C, T, H, W]
                        clip_tensor = torch.stack([
                            torch.from_numpy(frame).permute(2, 0, 1).float() / 255.0
                            for frame in frames
                        ]).to(self.device)
                        
                        # Apply half precision if enabled
                        if self.use_half_precision and self.device.type == 'cuda':
                            clip_tensor = clip_tensor.half()
                            
                        # Ensure model is in eval mode
                        self.action_model.eval()
                        
                        # Make prediction
                        start_time = time.time()
                        with torch.no_grad():
                            # Add batch dimension [1, C, T, H, W]
                            clip_tensor = clip_tensor.unsqueeze(0)
                            outputs = self.action_model(clip_tensor)
                            
                            # Get prediction (assuming binary classification)
                            if isinstance(outputs, tuple):
                                outputs = outputs[0]
                                
                            prediction = torch.sigmoid(outputs).item() if outputs.numel() == 1 else outputs.softmax(dim=1)[0, 1].item()
                            
                        prediction_time = time.time() - start_time
                        self.prediction_times.append(prediction_time)
                        
                        # Add prediction to person's label queue
                        label = "SUSPICIOUS" if prediction > 0.5 else "NORMAL"
                        tracker.add_label(label, prediction)
                        
                        if self.verbose:
                            print(f"🏷️  Person {tracker.person_id}: {label} ({prediction:.3f})")
                            
                    except Exception as e:
                        if self.verbose:
                            print(f"❌ Prediction error: {e}")
            
            # Small sleep to prevent CPU hogging
            time.sleep(0.01)
            
    def display_thread(self):
        """Thread for displaying annotated video frames"""
        print("📺 Starting display thread...")
        
        # Create display window
        cv2.namedWindow("Video Processor", cv2.WINDOW_NORMAL)
        cv2.resizeWindow("Video Processor", self.display_width, self.display_height)
        
        # Calculate grid layout based on number of sources
        num_sources = len(self.video_sources)
        grid_cols = min(3, num_sources)
        grid_rows = (num_sources + grid_cols - 1) // grid_cols
        
        # Frame storage for grid display
        grid_frames = {}
        
        while not self.stop_event.is_set():
            start_time = time.time()
            
            # Get latest frame from each source
            for source in self.video_sources:
                if source.frame_queue.empty():
                    continue
                    
                # Peek at the latest frame without removing it
                try:
                    # Get all frames to find the latest
                    frames = []
                    while not source.frame_queue.empty():
                        frame_data = source.frame_queue.get(block=False)
                        frames.append(frame_data)
                        source.frame_queue.task_done()
                        
                    # Put all frames back except the last one
                    for i in range(len(frames) - 1):
                        source.frame_queue.put(frames[i], block=False)
                        
                    # Process the latest frame
                    if frames:
                        frame_data = frames[-1]
                        frame = frame_data['frame'].copy()
                        source_id = frame_data['source_id']
                        
                        # Get detections if available
                        if 'detections' in frame_data:
                            boxes = frame_data['detections']['boxes']
                            confidences = frame_data['detections']['confidences']
                            ids = frame_data['detections']['ids']
                            
                            # Draw bounding boxes with labels
                            for box, conf, person_id in zip(boxes, confidences, ids):
                                unique_id = f"{source_id}_{person_id}"
                                
                                if unique_id in self.person_trackers:
                                    # Get latest prediction
                                    label, score = self.person_trackers[unique_id].get_latest_label()
                                    
                                    # Draw box
                                    x1, y1, x2, y2 = map(int, box)
                                    
                                    # Choose color based on prediction
                                    if label == "SUSPICIOUS":
                                        color = (0, 0, 255)  # Red for suspicious
                                    else:
                                        color = (0, 255, 0)  # Green for normal
                                        
                                    # Draw rectangle
                                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                                    
                                    # Add label text
                                    label_text = f"{label} ({score:.2f})" if label else f"Person {person_id}"
                                    cv2.putText(frame, label_text, (x1, y1 - 10), 
                                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                        
                        # Add source name
                        cv2.putText(frame, frame_data['source_name'], (10, 30), 
                                    cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
                        
                        # Store frame for grid display
                        grid_frames[source_id] = frame
                        
                except (queue.Empty, queue.Full):
                    pass
                    
            # Create grid display
            if grid_frames:
                # Calculate frame size in grid
                grid_width = self.display_width // grid_cols
                grid_height = self.display_height // grid_rows
                
                # Create empty grid
                grid = np.zeros((self.display_height, self.display_width, 3), dtype=np.uint8)
                
                # Place frames in grid
                for source_id, frame in grid_frames.items():
                    row = source_id // grid_cols
                    col = source_id % grid_cols
                    
                    # Resize frame to fit grid cell
                    resized_frame = cv2.resize(frame, (grid_width, grid_height))
                    
                    # Place in grid
                    y_start = row * grid_height
                    y_end = y_start + grid_height
                    x_start = col * grid_width
                    x_end = x_start + grid_width
                    
                    # Ensure we don't exceed grid dimensions
                    y_end = min(y_end, self.display_height)
                    x_end = min(x_end, self.display_width)
                    
                    # Copy resized frame to grid
                    grid[y_start:y_end, x_start:x_end] = resized_frame[:y_end-y_start, :x_end-x_start]
                
                # Display grid
                cv2.imshow("Video Processor", grid)
                
            # Handle keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                self.stop_event.set()
                break
            elif key == ord('p'):
                if self.pause_event.is_set():
                    self.pause_event.clear()
                    print("▶️ Resumed")
                else:
                    self.pause_event.set()
                    print("⏸️ Paused")
                    
            # Calculate display time
            display_time = time.time() - start_time
            self.display_times.append(display_time)
            
            # Limit frame rate
            time.sleep(max(0.001, 1/30 - display_time))
            
        cv2.destroyAllWindows()
        
    def cleanup_thread(self):
        """Thread for cleaning up inactive person trackers"""
        print("🧹 Starting cleanup thread...")
        
        while not self.stop_event.is_set():
            # Sleep for a while before checking
            time.sleep(5.0)
            
            if self.pause_event.is_set():
                continue
                
            # Get current time
            current_time = time.time()
            
            # Find inactive trackers (not seen for more than 10 seconds)
            inactive_trackers = []
            for person_id, tracker in self.person_trackers.items():
                if current_time - tracker.last_seen > 10.0:
                    inactive_trackers.append(person_id)
                    
            # Remove inactive trackers
            for person_id in inactive_trackers:
                if self.verbose:
                    print(f"🧹 Removing inactive tracker: {person_id}")
                del self.person_trackers[person_id]
                
    def print_performance_stats(self):
        """Print performance statistics"""
        print("\n📊 Performance Statistics:")
        
        if self.detection_times:
            avg_detection = sum(self.detection_times) / len(self.detection_times)
            print(f"  Detection: {avg_detection:.4f}s per frame ({1/avg_detection:.1f} FPS)")
            
        if self.prediction_times:
            avg_prediction = sum(self.prediction_times) / len(self.prediction_times)
            print(f"  Prediction: {avg_prediction:.4f}s per clip ({1/avg_prediction:.1f} clips/s)")
            
        if self.display_times:
            avg_display = sum(self.display_times) / len(self.display_times)
            print(f"  Display: {avg_display:.4f}s per frame ({1/avg_display:.1f} FPS)")
            
        print(f"  Active person trackers: {len(self.person_trackers)}")
        print(f"  Device: {self.device}")
        print(f"  Precision: {'Half (FP16)' if self.use_half_precision else 'Full (FP32)'}")
        
    def start(self):
        """Start all threads and processing"""
        print("🚀 Starting video processor...")
        
        # Initialize models
        self.initialize_models()
        
        # Start video sources
        for source in self.video_sources:
            source.start()
            
        # Create and start threads
        self.threads = [
            threading.Thread(target=self.detection_thread, name="Detection"),
            threading.Thread(target=self.prediction_thread, name="Prediction"),
            threading.Thread(target=self.display_thread, name="Display"),
            threading.Thread(target=self.cleanup_thread, name="Cleanup")
        ]
        
        # Start all threads
        for thread in self.threads:
            thread.daemon = True
            thread.start()
            
        print("✅ All threads started")
        
        try:
            # Wait for stop event
            while not self.stop_event.is_set():
                time.sleep(0.1)
        except KeyboardInterrupt:
            print("\n⚠️ Keyboard interrupt detected")
            self.stop_event.set()
            
        # Wait for threads to finish
        for thread in self.threads:
            thread.join(timeout=1.0)
            
        # Stop video sources
        for source in self.video_sources:
            source.stop()
            
        # Print performance stats
        self.print_performance_stats()
        
        print("✅ Video processor stopped")

def main():
    """Main function to start processing"""
    print("🎥 Multi-Source Video Processor")
    print("=" * 70)
    
    # Validate video sources
    valid_sources = []
    for source in VIDEO_SOURCES:
        if os.path.exists(source):
            valid_sources.append(source)
        else:
            print(f"⚠️ Warning: Source not found: {source}")
    
    if not valid_sources:
        print("❌ Error: No valid video sources found. Please check VIDEO_SOURCES in the script.")
        return
    
    print(f"📹 Processing {len(valid_sources)} video sources:")
    for i, source in enumerate(valid_sources):
        print(f"  {i+1}. {source}")
    
    # Create and start processor
    processor = VideoProcessor(
        video_sources=valid_sources,
        model_path=DEFAULT_MODEL_PATH,
        yolo_path=DEFAULT_YOLO_PATH,
        yolo_trt_path=DEFAULT_YOLO_TRT_PATH,
        use_tensorrt=USE_TENSORRT,
        display_width=DEFAULT_DISPLAY_WIDTH,
        display_height=DEFAULT_DISPLAY_HEIGHT,
        use_half_precision=USE_HALF_PRECISION,
        verbose=VERBOSE
    )
    
    processor.start()

if __name__ == "__main__":
    main()
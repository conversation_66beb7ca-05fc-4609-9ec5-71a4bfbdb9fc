import torch
import torch_tensorrt
from cnn import Classifier
import os

def main():
    """
    Compiles a PyTorch Classifier model to a TensorRT-optimized TorchScript module.
    """
    # Define paths
    model_path = 'data/models/trained_model_cnn.pt'
    compiled_model_path = 'data/models/trained_model_cnn_trt.engine'
    
    # Check if the trained model exists
    if not os.path.exists(model_path):
        print(f"Error: Model file not found at {model_path}")
        return

    # Set device
    device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
    if not torch.cuda.is_available():
        print("Error: CUDA is required for TensorRT compilation, but it is not available.")
        return

    print(f"Using device: {device}")

    # Initialize the model
    model = Classifier()
    
    # Load the checkpoint
    print(f"Loading model from {model_path}...")
    try:
        checkpoint = torch.load(model_path, map_location=device)
        # The state dict is stored under the 'model_state_dict' key
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            # Fallback for checkpoints that only contain the state_dict
            print("Warning: 'model_state_dict' key not found. Attempting to load the entire file as state_dict.")
            model.load_state_dict(checkpoint)
        print("Model loaded successfully.")
    except Exception as e:
        print(f"Error loading model checkpoint: {e}")
        return

    # Set the model to evaluation mode and move to the GPU
    model.eval().to(device)

    # Define an example input tensor with the correct shape for the model
    # Shape: [batch_size, channels, num_frames, height, width]
    example_input = torch.randn(1, 3, 16, 112, 112).to(device)

    print("Tracing the model...")
    try:
        traced_model = torch.jit.trace(model, example_input)
        print("Model traced successfully.")
    except Exception as e:
        print(f"Error during model tracing: {e}")
        return

    print("Compiling the model with TensorRT...")
    
    try:
        # Compile the traced model using torch_tensorrt
        trt_model = torch_tensorrt.compile(traced_model,
            inputs=[torch_tensorrt.Input(example_input.shape)],
            enabled_precisions={torch.float, torch.half},   # Use FP32 and FP16 precision
            ir='torchscript'
        )

        # Save the compiled TorchScript module
        torch.jit.save(trt_model, compiled_model_path)
        print(f"Model successfully compiled and saved to {compiled_model_path}")

    except Exception as e:
        print(f"An error occurred during TensorRT compilation: {e}")
        print("Please check your TensorRT installation and GPU compatibility.")

if __name__ == '__main__':
    main()
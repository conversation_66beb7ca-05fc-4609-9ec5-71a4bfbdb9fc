#!/usr/bin/env python3
"""
Multi-threaded Video Processing System with Real-time Person Detection and Action Classification
Uses separate threads for video capture, person detection/tracking, action prediction, and display
Architecture:
1. VideoSource threads: Capture frames from video sources into queues
2. Detection thread: Reads frames, detects persons, tracks with DeepSORT, crops persons
3. Prediction thread: Reads 16-frame clips from person queues, runs video classifier
4. Display thread: Annotates original frames with bounding boxes and labels, displays video
"""

import cv2
import os
import time
import threading
import queue
import numpy as np
import torch

# Import required modules
from utils.detection import detect_persons
from utils.cropping import crop_person
from utils.models import load_yolo_model, load_action_transformer

# ============================================================================
# 📹 CONFIGURATION - MODIFY THESE SETTINGS:
# ============================================================================

# Video sources - modify this list to add your video sources
VIDEO_SOURCES = [
    "data/feed/video1.mp4",
    "data/feed/video2.mp4",
    # Add more video sources as needed
]

# Model paths
YOLO_PATH = 'data/models/yolo11s.pt'
YOLO_TRT_PATH = 'data/models/yolo11s.engine'
VIDEO_CLASSIFIER_PATH = 'data/models/trained_model_cnn.pt'

# Display settings
DISPLAY_WIDTH = 640
DISPLAY_HEIGHT = 480

# Processing settings
QUEUE_SIZE = 100
CLIP_FRAMES = 16
CONFIDENCE_THRESHOLD = 0.3
CROP_SIZE = (224, 224)

# Toggle options
USE_TENSORRT = False  # Set to True to use TensorRT YOLO model
USE_HALF_PRECISION = True  # Set to True to use FP16 precision
VERBOSE = False  # Set to True for detailed logging

# ============================================================================

class VideoSource:
    """Captures frames from a video source and puts them in a queue"""
    
    def __init__(self, source_id: int, source_path: str, queue_size: int = QUEUE_SIZE):
        self.source_id = source_id
        self.source_path = source_path
        self.frame_queue = queue.Queue(maxsize=queue_size)
        self.stop_event = threading.Event()
        self.thread = None
        self.source_name = os.path.basename(source_path)
        self.fps = 30  # Default FPS
        
    def start(self):
        """Start the video capture thread"""
        self.thread = threading.Thread(target=self._capture_frames, name=f"VideoSource-{self.source_id}")
        self.thread.daemon = True
        self.thread.start()
        if VERBOSE:
            print(f"✅ Started video source {self.source_id}: {self.source_name}")
        return self
        
    def _capture_frames(self):
        """Capture frames from video source and put them in queue"""
        cap = cv2.VideoCapture(self.source_path)
        if not cap.isOpened():
            print(f"❌ Could not open video source: {self.source_path}")
            return
            
        # Get video properties
        self.fps = cap.get(cv2.CAP_PROP_FPS) or 30
        frame_delay = 1.0 / self.fps
        
        frame_count = 0
        last_time = time.time()
        
        while not self.stop_event.is_set():
            ret, frame = cap.read()
            if not ret:
                if VERBOSE:
                    print(f"📹 Video source {self.source_id} ended")
                break
                
            # Maintain frame rate
            current_time = time.time()
            elapsed = current_time - last_time
            if elapsed < frame_delay:
                time.sleep(frame_delay - elapsed)
            
            # Prepare frame data
            frame_data = {
                'frame': frame,
                'source_id': self.source_id,
                'frame_number': frame_count,
                'timestamp': time.time()
            }
            
            try:
                self.frame_queue.put(frame_data, block=False)
            except queue.Full:
                if VERBOSE:
                    print(f"⚠️ Frame queue full for source {self.source_id}, dropping frame")
                    
            frame_count += 1
            last_time = time.time()
            
        cap.release()
        if VERBOSE:
            print(f"✅ Video source {self.source_id} capture thread finished")
        
    def stop(self):
        """Stop the video capture thread"""
        self.stop_event.set()
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=2.0)

class PersonTracker:
    """Tracks a specific person and manages their frame queue"""
    
    def __init__(self, person_id: str, queue_size: int = QUEUE_SIZE):
        self.person_id = person_id
        self.frame_queue = queue.Queue(maxsize=queue_size)
        self.label_queue = queue.Queue(maxsize=queue_size)
        self.last_seen = time.time()
        self.frames_processed = 0
        self.current_box = None
        
    def add_frame(self, cropped_frame, box):
        """Add a cropped frame to this person's queue"""
        try:
            frame_data = {
                'crop': cropped_frame,
                'box': box,
                'timestamp': time.time()
            }
            self.frame_queue.put(frame_data, block=False)
            self.current_box = box
            self.last_seen = time.time()
            self.frames_processed += 1
            return True
        except queue.Full:
            return False
            
    def add_label(self, label, confidence):
        """Add a prediction label to this person's queue"""
        try:
            label_data = {
                'label': label,
                'confidence': confidence,
                'timestamp': time.time()
            }
            self.label_queue.put(label_data, block=False)
            return True
        except queue.Full:
            return False
            
    def get_clip_frames(self, num_frames=CLIP_FRAMES):
        """Get a clip of frames for prediction"""
        frames = []
        temp_frames = []
        
        # Collect frames without removing them from queue
        while len(frames) < num_frames and not self.frame_queue.empty():
            try:
                frame_data = self.frame_queue.get(block=False)
                frames.append(frame_data['crop'])
                temp_frames.append(frame_data)
            except queue.Empty:
                break
                
        # Put frames back if we don't have enough
        if len(frames) < num_frames:
            for frame_data in temp_frames:
                try:
                    self.frame_queue.put(frame_data, block=False)
                except queue.Full:
                    pass
            return None
            
        return frames
        
    def is_active(self, timeout=5.0):
        """Check if person has been seen recently"""
        return (time.time() - self.last_seen) < timeout

class MultiThreadedVideoProcessor:
    """Main processor class that coordinates all threads"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.video_sources = []
        self.person_trackers = {}
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        
        # Performance tracking
        self.start_time = None
        self.frames_processed = 0
        self.detections_made = 0
        self.predictions_made = 0
        
        # Models (will be initialized later)
        self.yolo_model = None
        self.action_model = None
        
        # Display data
        self.display_frames = {}  # source_id -> latest frame with annotations
        self.display_lock = threading.Lock()
        
    def initialize_models(self):
        """Initialize YOLO and action classification models"""
        print(f"🔄 Initializing models on {self.device}...")

        # Load YOLO model (choose TensorRT or regular based on setting)
        yolo_path = YOLO_TRT_PATH if USE_TENSORRT else YOLO_PATH
        print(f"🔄 Loading YOLO model from: {yolo_path}")

        self.yolo_model = load_yolo_model(
            yolo_path,
            device=0 if self.device.type == 'cuda' else 'cpu',
            use_half_precision=USE_HALF_PRECISION
        )

        if self.yolo_model is None:
            raise RuntimeError("❌ Failed to load YOLO model!")

        # Load action classification model
        print(f"🔄 Loading video classifier from: {VIDEO_CLASSIFIER_PATH}")
        self.action_model = load_action_transformer(VIDEO_CLASSIFIER_PATH, self.device)

        if self.action_model is None:
            raise RuntimeError("❌ Failed to load video classifier model!")

        # Apply half precision if requested and on CUDA
        if USE_HALF_PRECISION and self.device.type == 'cuda':
            print("🔄 Using half precision (FP16)...")
            self.action_model = self.action_model.half()
            # Note: YOLO model precision is handled in load_yolo_model function

        # Set models to evaluation mode
        self.action_model.eval()
        if hasattr(self.action_model, 'backbone'):
            self.action_model.backbone.eval()

        print("✅ Models initialized successfully")
        
    def setup_video_sources(self, source_paths):
        """Setup video sources from list of paths"""
        for i, path in enumerate(source_paths):
            if os.path.exists(path):
                source = VideoSource(i, path)
                self.video_sources.append(source)
                if VERBOSE:
                    print(f"📹 Added video source {i}: {os.path.basename(path)}")
            else:
                print(f"⚠️ Video source not found: {path}")
                
        if not self.video_sources:
            raise RuntimeError("❌ No valid video sources found!")

    def detection_thread(self):
        """Thread for person detection and tracking using DeepSORT"""
        if VERBOSE:
            print("🔍 Starting detection thread...")

        while not self.stop_event.is_set():
            if self.pause_event.is_set():
                time.sleep(0.1)
                continue

            # Process frames from all video sources
            for source in self.video_sources:
                try:
                    frame_data = source.frame_queue.get(block=False)
                except queue.Empty:
                    continue

                frame = frame_data['frame']
                source_id = frame_data['source_id']

                # Detect persons using YOLO with tracking
                try:
                    boxes, confidences, ids = detect_persons(
                        self.yolo_model,
                        frame,
                        confidence_threshold=CONFIDENCE_THRESHOLD,
                        device_index=0 if self.device.type == 'cuda' else None
                    )

                    self.detections_made += 1

                    # Process each detected person
                    for box, _, person_id in zip(boxes, confidences, ids):
                        if person_id is None:
                            continue

                        # Create unique ID combining source and person ID
                        unique_id = f"{source_id}_{person_id}"

                        # Create tracker if this is a new person
                        if unique_id not in self.person_trackers:
                            self.person_trackers[unique_id] = PersonTracker(unique_id)
                            if VERBOSE:
                                print(f"👤 New person detected: {unique_id}")

                        # Crop person and add to their queue
                        crop = crop_person(frame, box, CROP_SIZE)
                        if crop is not None:
                            # Convert crop to GPU tensor if using CUDA
                            if self.device.type == 'cuda':
                                crop_tensor = torch.from_numpy(crop).to(self.device)
                                if USE_HALF_PRECISION:
                                    crop_tensor = crop_tensor.half()
                                crop = crop_tensor.cpu().numpy()  # Keep as numpy for queue

                            self.person_trackers[unique_id].add_frame(crop, box)

                    # Store frame data for display thread
                    with self.display_lock:
                        self.display_frames[source_id] = {
                            'frame': frame.copy(),
                            'boxes': boxes,
                            'confidences': confidences,
                            'ids': ids,
                            'timestamp': frame_data['timestamp']
                        }

                except Exception as e:
                    if 'nms' not in str(e).lower() and VERBOSE:
                        print(f"⚠️ Detection error: {e}")

                self.frames_processed += 1

            # Clean up inactive person trackers
            inactive_trackers = [
                pid for pid, tracker in self.person_trackers.items()
                if not tracker.is_active()
            ]
            for pid in inactive_trackers:
                del self.person_trackers[pid]
                if VERBOSE:
                    print(f"🗑️ Removed inactive person: {pid}")

            # Small sleep to prevent CPU hogging
            time.sleep(0.001)

        if VERBOSE:
            print("✅ Detection thread finished")

    def prediction_thread(self):
        """Thread for action classification from person clips"""
        if VERBOSE:
            print("🧠 Starting prediction thread...")

        while not self.stop_event.is_set():
            if self.pause_event.is_set():
                time.sleep(0.1)
                continue

            # Get all active person trackers
            active_trackers = list(self.person_trackers.values())
            if not active_trackers:
                time.sleep(0.1)
                continue

            # Collect clips from all persons for batch processing
            batch_clips = []
            batch_person_ids = []

            for tracker in active_trackers:
                clip_frames = tracker.get_clip_frames(CLIP_FRAMES)
                if clip_frames is not None:
                    batch_clips.append(clip_frames)
                    batch_person_ids.append(tracker.person_id)

            if not batch_clips:
                time.sleep(0.1)
                continue

            # Process batch of clips
            try:
                # Convert clips to tensor batch
                batch_tensor = []
                for clip in batch_clips:
                    # Stack frames into video tensor [T, H, W, C] -> [C, T, H, W]
                    clip_array = np.stack(clip, axis=0)  # [T, H, W, C]
                    clip_array = np.transpose(clip_array, (3, 0, 1, 2))  # [C, T, H, W]
                    batch_tensor.append(clip_array)

                # Stack into batch [B, C, T, H, W]
                batch_tensor = np.stack(batch_tensor, axis=0)
                batch_tensor = torch.from_numpy(batch_tensor).to(self.device)

                if USE_HALF_PRECISION and self.device.type == 'cuda':
                    batch_tensor = batch_tensor.half()
                else:
                    batch_tensor = batch_tensor.float()

                # Normalize to [0, 1]
                batch_tensor = batch_tensor / 255.0

                # Run batch prediction
                with torch.no_grad():
                    predictions = self.action_model(batch_tensor)
                    if predictions.dim() > 1:
                        predictions = torch.sigmoid(predictions).squeeze()
                    else:
                        predictions = torch.sigmoid(predictions)

                # Process predictions for each person
                for i, person_id in enumerate(batch_person_ids):
                    if person_id in self.person_trackers:
                        pred_value = predictions[i].item() if len(batch_person_ids) > 1 else predictions.item()
                        label = "Suspicious" if pred_value > 0.5 else "Normal"
                        confidence = pred_value if pred_value > 0.5 else (1.0 - pred_value)

                        self.person_trackers[person_id].add_label(label, confidence)

                        if VERBOSE and label == "Suspicious":
                            print(f"🚨 Suspicious behavior detected for person {person_id}: {confidence:.2f}")

                self.predictions_made += len(batch_clips)

            except Exception as e:
                if VERBOSE:
                    print(f"❌ Prediction error: {e}")

            # Small sleep to prevent CPU hogging
            time.sleep(0.01)

        if VERBOSE:
            print("✅ Prediction thread finished")

    def display_thread(self):
        """Thread for displaying annotated video frames"""
        if VERBOSE:
            print("📺 Starting display thread...")

        # Create display windows
        window_names = {}
        for source in self.video_sources:
            window_name = f"Video Source {source.source_id}: {source.source_name}"
            window_names[source.source_id] = window_name
            cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(window_name, DISPLAY_WIDTH, DISPLAY_HEIGHT)

        while not self.stop_event.is_set():
            if self.pause_event.is_set():
                time.sleep(0.1)
                continue

            with self.display_lock:
                display_data = self.display_frames.copy()

            if not display_data:
                time.sleep(0.033)  # ~30 FPS
                continue

            for source_id, frame_data in display_data.items():
                if source_id not in window_names:
                    continue

                frame = frame_data['frame'].copy()
                boxes = frame_data['boxes']
                confidences = frame_data['confidences']
                ids = frame_data['ids']

                # Draw bounding boxes and labels
                for box, _, person_id in zip(boxes, confidences, ids):
                    if person_id is None:
                        continue

                    unique_id = f"{source_id}_{person_id}"

                    # Get latest label for this person
                    label = "Unknown"
                    label_conf = 0.0
                    if unique_id in self.person_trackers:
                        tracker = self.person_trackers[unique_id]
                        if not tracker.label_queue.empty():
                            try:
                                label_data = tracker.label_queue.get(block=False)
                                label = label_data['label']
                                label_conf = label_data['confidence']
                                # Put it back for next frame
                                tracker.label_queue.put(label_data, block=False)
                            except (queue.Empty, queue.Full):
                                pass

                    # Draw bounding box
                    x1, y1, x2, y2 = map(int, box)
                    color = (0, 0, 255) if label == "Suspicious" else (0, 255, 0)  # Red for suspicious, green for normal
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)

                    # Draw label
                    label_text = f"ID:{person_id} {label} ({label_conf:.2f})"
                    label_size = cv2.getTextSize(label_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                    cv2.rectangle(frame, (x1, y1 - label_size[1] - 10),
                                (x1 + label_size[0], y1), color, -1)
                    cv2.putText(frame, label_text, (x1, y1 - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                # Add performance info
                fps_text = f"FPS: {self.frames_processed / max(1, time.time() - self.start_time):.1f}"
                cv2.putText(frame, fps_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                # Display frame
                cv2.imshow(window_names[source_id], frame)

            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                self.stop_event.set()
                break
            elif key == ord('p'):
                if self.pause_event.is_set():
                    self.pause_event.clear()
                    print("▶️ Resumed")
                else:
                    self.pause_event.set()
                    print("⏸️ Paused")

        # Clean up windows
        cv2.destroyAllWindows()
        if VERBOSE:
            print("✅ Display thread finished")

    def start_processing(self):
        """Start all processing threads"""
        print("🚀 Starting multi-threaded video processing...")
        self.start_time = time.time()

        # Start video sources
        for source in self.video_sources:
            source.start()

        # Start processing threads
        threads = [
            threading.Thread(target=self.detection_thread, name="Detection", daemon=True),
            threading.Thread(target=self.prediction_thread, name="Prediction", daemon=True),
            threading.Thread(target=self.display_thread, name="Display", daemon=True)
        ]

        for thread in threads:
            thread.start()
            if VERBOSE:
                print(f"✅ Started {thread.name} thread")

        # Wait for display thread to finish (user pressed 'q')
        threads[2].join()  # Display thread

        # Stop all processing
        self.stop_processing()

    def stop_processing(self):
        """Stop all processing threads and video sources"""
        print("🛑 Stopping video processing...")

        # Signal all threads to stop
        self.stop_event.set()

        # Stop video sources
        for source in self.video_sources:
            source.stop()

        # Print performance metrics
        self.print_performance_metrics()

    def print_performance_metrics(self):
        """Print final performance metrics"""
        if self.start_time is None:
            return

        total_time = time.time() - self.start_time
        avg_fps = self.frames_processed / max(1, total_time)

        print("\n" + "="*50)
        print("📊 PERFORMANCE METRICS")
        print("="*50)
        print(f"⏱️  Total processing time: {total_time:.2f} seconds")
        print(f"🎬 Total frames processed: {self.frames_processed}")
        print(f"📈 Average FPS: {avg_fps:.2f}")
        print(f"👁️  Total detections made: {self.detections_made}")
        print(f"🧠 Total predictions made: {self.predictions_made}")
        print(f"👤 Active person trackers: {len(self.person_trackers)}")
        print("="*50)

def main():
    """Main function to run the multi-threaded video processor"""
    try:
        # Create processor instance
        processor = MultiThreadedVideoProcessor()

        # Initialize models
        processor.initialize_models()

        # Setup video sources
        processor.setup_video_sources(VIDEO_SOURCES)

        print(f"\n📹 Configured {len(processor.video_sources)} video sources")
        print("🎮 Controls: 'q' to quit, 'p' to pause/resume")
        print("⚙️  Settings:")
        print(f"   - Display size: {DISPLAY_WIDTH}x{DISPLAY_HEIGHT}")
        print(f"   - Half precision: {USE_HALF_PRECISION}")
        print(f"   - TensorRT: {USE_TENSORRT}")
        print(f"   - Verbose: {VERBOSE}")
        print(f"   - Clip frames: {CLIP_FRAMES}")
        print(f"   - Confidence threshold: {CONFIDENCE_THRESHOLD}")

        # Start processing
        processor.start_processing()

    except KeyboardInterrupt:
        print("\n⚠️ Interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
